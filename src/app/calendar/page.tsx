"use client";

import { useState, lazy, Suspense, useCallback } from "react";
import { Header } from "@/components/shared/Header";
import { Footer } from "@/components/shared/Footer";
import { CalendarContainer } from "@/components/calendar/CalendarContainer";
import { useCalendarLogic } from "@/hooks/useCalendarLogic";
import { CalendarActions } from "@/components/calendar/CalendarActions";
import { ViewModeSelector } from "@/components/calendar/ViewModeSelector";
import { TournamentFilters } from "@/components/calendar/TournamentFilters";
import { Tournament } from "@/types/calendar";
import { ErrorBoundary } from "@/components/shared/ErrorBoundary";
import { ErrorFallback } from "@/components/shared/ErrorFallback";
import { LoadingState } from "@/components/ui/LoadingState";
import { useIsAdmin } from "@/hooks/useIsAdmin";
import { useFilters } from "@/hooks/useFilters";

// Dynamic imports for modals to reduce initial bundle size
const RegistrationModal = lazy(() => 
  import("@/components/calendar/RegistrationModal").then(mod => ({ 
    default: mod.RegistrationModal 
  }))
);

const EventFormModal = lazy(() => 
  import("@/components/calendar/EventFormModal").then(mod => ({ 
    default: mod.EventFormModal 
  }))
);

const EventSelectionModal = lazy(() => 
  import("@/components/calendar/EventSelectionModal").then(mod => ({ 
    default: mod.EventSelectionModal 
  }))
);

export default function CalendarPage() {
  // Filters functionality
  const {
    filters,
    updateFilter,
    resetFilters,
    hasActiveFilters,
  } = useFilters();
  
  const {
    currentMonth,
    currentYear,
    daysInMonth,
    firstDayOfMonth,
    selectedDate,
    selectedTournament,
    tournamentDays,
    isRegistrationModalOpen,
    isLoading,
    goToPreviousMonth,
    goToNextMonth,
    handleDayClick,
    handleTournamentClick,
    // setSelectedTournament,
    setIsRegistrationModalOpen,
    // View mode functionality
    viewMode,
    toggleViewMode
  } = useCalendarLogic(filters);
  
  // Admin status for conditional UI rendering
  const { isAdmin } = useIsAdmin();

  const [isEventFormOpen, setIsEventFormOpen] = useState(false);
  const [isEventSelectionOpen, setIsEventSelectionOpen] = useState(false);
  const [eventFormMode, setEventFormMode] = useState<'create' | 'edit'>('create');
  const [eventToEdit, setEventToEdit] = useState<Tournament | null>(null);
  const [preselectedDate, setPreselectedDate] = useState<Date | null>(null);

  const handleAddEvent = useCallback(() => {
    setEventFormMode('create');
    setEventToEdit(null);
    setPreselectedDate(selectedDate);
    setIsEventFormOpen(true);
  }, [selectedDate]);

  const handleCreateEvent = useCallback((date: Date) => {
    setEventFormMode('create');
    setEventToEdit(null);
    setPreselectedDate(date);
    setIsEventFormOpen(true);
  }, []);

  const handleEditEvent = useCallback(() => {
    setIsEventSelectionOpen(true);
  }, []);

  const handleSelectEvent = useCallback((tournament: Tournament) => {
    setEventToEdit(tournament);
    setEventFormMode('edit');
    setIsEventSelectionOpen(false);
    setIsEventFormOpen(true);
  }, []);

  const handleEditTournament = useCallback((tournament: Tournament) => {
    setEventToEdit(tournament);
    setEventFormMode('edit');
    setIsEventFormOpen(true);
  }, []);

  const handleCloseRegistrationModal = useCallback(() => {
    setIsRegistrationModalOpen(false);
  }, [setIsRegistrationModalOpen]);

  const handleCloseEventFormModal = useCallback(() => {
    setIsEventFormOpen(false);
  }, []);

  const handleCloseEventSelectionModal = useCallback(() => {
    setIsEventSelectionOpen(false);
  }, []);

  const handleOpenRegistrationModal = useCallback(() => {
    setIsRegistrationModalOpen(true);
  }, [setIsRegistrationModalOpen]);


  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 text-white">
      <Header currentPage="calendar" />

      <main className="container mx-auto p-4 sm:p-6">
        {/* Header Section */}
        <div className="flex justify-between items-center mb-4 sm:mb-6">
          <h1 className="text-2xl sm:text-3xl font-bold">Calendario Tornei</h1>
          
          {/* Desktop ViewMode and Actions */}
          <div className="hidden sm:flex items-center gap-2">
            <ViewModeSelector
              viewMode={viewMode}
              onChange={toggleViewMode}
              disabled={isLoading}
            />
            {isAdmin && (
              <CalendarActions 
                onAddEvent={handleAddEvent}
                onEditEvent={handleEditEvent}
              />
            )}
          </div>
        </div>
        
        {/* Mobile ViewModeSelector and Actions */}
        <div className="sm:hidden mb-4 flex justify-center items-center gap-2">
          <ViewModeSelector
            viewMode={viewMode}
            onChange={toggleViewMode}
            disabled={isLoading}
          />
          {isAdmin && (
            <CalendarActions 
              onAddEvent={handleAddEvent}
              onEditEvent={handleEditEvent}
            />
          )}
        </div>
        
        {/* Filters Section - Only visible in cards mode */}
        {viewMode === 'cards' && (
          <div className="mb-4">
            <TournamentFilters
              filters={filters}
              onUpdateFilter={updateFilter}
              onResetFilters={resetFilters}
              hasActiveFilters={hasActiveFilters}
              disabled={isLoading}
            />
          </div>
        )}
        
        <ErrorBoundary
          level="section"
          fallback={<ErrorFallback variant="detailed" />}
          context={{ component: 'CalendarContainer', page: 'calendar' }}
        >
          <CalendarContainer
            currentMonth={currentMonth}
            currentYear={currentYear}
            firstDayOfMonth={firstDayOfMonth}
            daysInMonth={daysInMonth}
            tournamentDays={tournamentDays}
            selectedDate={selectedDate}
            selectedTournament={selectedTournament}
            isLoading={isLoading}
            onPrevMonth={goToPreviousMonth}
            onNextMonth={goToNextMonth}
            onDayClick={handleDayClick}
            onTournamentClick={handleTournamentClick}
            onRegistrationClick={handleOpenRegistrationModal}
            onEditClick={handleEditTournament}
            onCreateEvent={handleCreateEvent}
            viewMode={viewMode}
            filters={filters}
          />
        </ErrorBoundary>
      </main>
      
      <Footer />

      <ErrorBoundary
        level="component"
        fallback={<ErrorFallback variant="minimal" />}
        context={{ component: 'RegistrationModal', page: 'calendar' }}
      >
        <Suspense fallback={<LoadingState text="Caricamento..." />}>
          <RegistrationModal
            tournament={selectedTournament}
            isOpen={isRegistrationModalOpen}
            onClose={handleCloseRegistrationModal}
          />
        </Suspense>
      </ErrorBoundary>

      <ErrorBoundary
        level="component"
        fallback={<ErrorFallback variant="minimal" />}
        context={{ component: 'EventFormModal', page: 'calendar' }}
      >
        <Suspense fallback={<LoadingState text="Caricamento..." />}>
          <EventFormModal
            isOpen={isEventFormOpen}
            onClose={handleCloseEventFormModal}
            initialData={eventToEdit}
            mode={eventFormMode}
            preselectedDate={preselectedDate}
          />
        </Suspense>
      </ErrorBoundary>

      <ErrorBoundary
        level="component"
        fallback={<ErrorFallback variant="minimal" />}
        context={{ component: 'EventSelectionModal', page: 'calendar' }}
      >
        <Suspense fallback={<LoadingState text="Caricamento..." />}>
          <EventSelectionModal
            isOpen={isEventSelectionOpen}
            onClose={handleCloseEventSelectionModal}
            onSelectEvent={handleSelectEvent}
          />
        </Suspense>
      </ErrorBoundary>
    </div>
  );
} 