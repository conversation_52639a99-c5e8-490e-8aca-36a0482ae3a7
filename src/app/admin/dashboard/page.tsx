'use client';

import { lazy, Suspense } from 'react';
import { AdminGuard } from '@/components/admin/AdminGuard';
import { Header } from '@/components/shared/Header';
import { ErrorBoundary } from '@/components/shared/ErrorBoundary';
import { ErrorFallback } from '@/components/shared/ErrorFallback';
import { LoadingState } from '@/components/ui/LoadingState';

// Dynamic import for ProfileForm to reduce initial bundle size
const ProfileForm = lazy(() => 
  import('@/components/admin/ProfileForm').then(mod => ({ 
    default: mod.ProfileForm 
  }))
);

export default function AdminDashboardPage() {
  return (
    <AdminGuard>
      <div className="min-h-screen bg-gradient-to-br from-blue-950 via-sky-900 to-blue-900 text-white">
        <Header currentPage="home" />
        
        <main className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            <div className="mb-8">
              <h1 className="text-2xl font-bold mb-2">Pannello Amministratore</h1>
              <p className="text-blue-300">
                Gestisci le tue impostazioni e preferenze dell&apos;account
              </p>
            </div>
            
            <ErrorBoundary
              level="section"
              fallback={<ErrorFallback variant="detailed" />}
              context={{ component: 'ProfileForm', page: 'admin-dashboard' }}
            >
              <Suspense fallback={<LoadingState text="Caricamento pannello..." />}>
                <ProfileForm />
              </Suspense>
            </ErrorBoundary>
          </div>
        </main>
      </div>
    </AdminGuard>
  );
} 