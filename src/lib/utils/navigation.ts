import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

/**
 * Custom hook for prefetching routes on hover or when visible
 * @param routes - Array of routes to prefetch
 */
export function usePrefetchRoutes(routes: string[]) {
  const router = useRouter();

  useEffect(() => {
    // Prefetch routes after initial load
    const timer = setTimeout(() => {
      routes.forEach(route => {
        router.prefetch(route);
      });
    }, 2000); // Wait 2 seconds after mount to avoid blocking initial render

    return () => clearTimeout(timer);
  }, [routes, router]);
}

/**
 * Prefetch a route on hover
 * @param route - Route to prefetch
 * @returns Event handler for mouse enter
 */
export function usePrefetchOnHover(route: string) {
  const router = useRouter();
  
  return () => {
    router.prefetch(route);
  };
}

/**
 * List of critical routes to prefetch
 */
export const CRITICAL_ROUTES = [
  '/',
  '/calendar',
  '/classifica',
  '/tools',
] as const;

/**
 * List of secondary routes to prefetch (lower priority)
 */
export const SECONDARY_ROUTES = [
  '/admin',
  '/admin/profile',
  '/auth/error',
  '/login',
  '/profile',
] as const;

/**
 * Hook to prefetch critical routes on app load
 */
export function usePrefetchCriticalRoutes() {
  usePrefetchRoutes([...CRITICAL_ROUTES]);
}

/**
 * Hook to prefetch secondary routes after critical routes
 */
export function usePrefetchSecondaryRoutes() {
  const router = useRouter();

  useEffect(() => {
    // Prefetch secondary routes with a longer delay
    const timer = setTimeout(() => {
      SECONDARY_ROUTES.forEach(route => {
        router.prefetch(route);
      });
    }, 5000); // Wait 5 seconds for secondary routes

    return () => clearTimeout(timer);
  }, [router]);
}
