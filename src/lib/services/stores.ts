import { supabase } from '../supabase/client';
import type { Database } from '../supabase/types';

// type Store = Database['public']['Tables']['stores']['Row'];
type InsertStore = Database['public']['Tables']['stores']['Insert'];
type UpdateStore = Database['public']['Tables']['stores']['Update'];

export const storesService = {
  async getAll() {
    const { data, error } = await supabase
      .from('stores')
      .select('*')
      .order('name');
    
    if (error) throw error;
    return data;
  },

  async getById(id: string) {
    const { data, error } = await supabase
      .from('stores')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async create(store: InsertStore) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data, error } = await (supabase as any)
      .from('stores')
      .insert(store)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async update(id: string, store: UpdateStore) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data, error } = await (supabase as any)
      .from('stores')
      .update(store)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string) {
    const { error } = await supabase
      .from('stores')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
}; 