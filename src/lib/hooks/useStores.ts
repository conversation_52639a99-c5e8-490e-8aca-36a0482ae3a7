import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { storesService } from '../services/stores';
import type { Database } from '../supabase/types';

// type Store = Database['public']['Tables']['stores']['Row'];
type InsertStore = Database['public']['Tables']['stores']['Insert'];
type UpdateStore = Database['public']['Tables']['stores']['Update'];

export function useStores() {
  return useQuery({
    queryKey: ['stores'],
    queryFn: () => storesService.getAll()
  });
}

export function useStore(id: string) {
  return useQuery({
    queryKey: ['stores', id],
    queryFn: () => storesService.getById(id)
  });
}

export function useCreateStore() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (store: InsertStore) => storesService.create(store),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stores'] });
    }
  });
}

export function useUpdateStore() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, store }: { id: string; store: UpdateStore }) => 
      storesService.update(id, store),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['stores'] });
      queryClient.invalidateQueries({ queryKey: ['stores', id] });
    }
  });
}

export function useDeleteStore() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => storesService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['stores'] });
    }
  });
} 